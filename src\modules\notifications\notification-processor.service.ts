import { notificationClient } from '../../shared/database/connection';
import {
  NotificationProcessorJobData,
  ContractExpirationNotificationType,
  JobProcessingError,
} from '../../shared/queue/types';
import logger from '../../shared/logger/logger';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { $Enums } from '@/generated/prisma-notification';

// ============================================================================
// NOTIFICATION PROCESSOR SERVICE
// ============================================================================

export class NotificationProcessorService {
  /**
   * Processes a contract expiration notification job
   */
  async processContractExpiration(
    jobData: NotificationProcessorJobData
  ): Promise<void> {
    try {
      logger.info('Processing contract expiration notification', {
        contractId: jobData.contractId,
        entityType: jobData.entityType,
        entityUuid: jobData.entityUuid,
      });

      // Check if notification already exists to avoid duplicates
      const existingNotification = await this.findExistingNotification(jobData);
      if (existingNotification) {
        logger.info('Notification already exists, skipping', {
          contractId: jobData.contractId,
          notificationId: existingNotification.id,
        });
        return;
      }

      // Create the notification
      const notification = await this.createNotification(jobData);

      logger.info('Contract expiration notification created successfully', {
        contractId: jobData.contractId,
        notificationId: notification.id,
        notificationType: jobData.metadata?.notificationType,
      });
    } catch (error) {
      logger.error('Error processing contract expiration notification', {
        error,
        jobData,
      });

      throw new JobProcessingError(
        'Failed to process contract expiration notification',
        'process-contract-expiration',
        jobData,
        error as Error
      );
    }
  }

  /**
   * Checks if a notification already exists for this contract
   */
  private async findExistingNotification(
    jobData: NotificationProcessorJobData
  ) {
    const referenceId = `contract-${jobData.contractId}-v${jobData.currentVersion}`;

    return await notificationClient.notification.findFirst({
      where: {
        referenceId,
        type: this.mapNotificationType(jobData.metadata?.notificationType),
        status: {
          in: ['PENDING', 'SENT', 'DELIVERED'],
        },
      },
    });
  }

  /**
   * Creates a new notification in the database
   */
  private async createNotification(jobData: NotificationProcessorJobData) {
    const notificationType = jobData.metadata?.notificationType;
    const daysUntilExpiration = jobData.metadata?.daysUntilExpiration || 0;

    const { title, message } = this.generateNotificationContent(
      jobData,
      notificationType,
      daysUntilExpiration
    );

    const referenceId = `contract-${jobData.contractId}-v${jobData.currentVersion}`;

    // Determine user ID - this would typically come from the entity relationship
    // For now, we'll use a placeholder or derive it from the entity
    const userId = jobData.userId || (await this.getUserIdFromEntity(jobData));

    const notification = await notificationClient.notification.create({
      data: {
        userId,
        type: this.mapNotificationType(notificationType),
        priority: this.determinePriority(daysUntilExpiration),
        title,
        message,
        data: {
          contractId: jobData.contractId,
          contractType: jobData.contractType,
          entityType: jobData.entityType,
          entityUuid: jobData.entityUuid,
          expirationDate: jobData.expirationDate.toISOString(),
          daysUntilExpiration,
          currentVersion: jobData.currentVersion,
          filePath: jobData.metadata?.filePath,
          observations: jobData.metadata?.observations,
        },
        channels: ['IN_APP', 'EMAIL'], // Default channels
        scheduledFor: null, // Send immediately
        expiresAt: this.calculateExpirationDate(daysUntilExpiration),
        source: 'CONTRACT_MONITOR',
        referenceId,
        tags: [
          'contract',
          'expiration',
          jobData.contractType.toLowerCase(),
          jobData.entityType.toLowerCase(),
        ],
      },
    });

    return notification;
  }

  /**
   * Generates notification title and message based on contract data
   */
  private generateNotificationContent(
    jobData: NotificationProcessorJobData,
    notificationType: ContractExpirationNotificationType,
    daysUntilExpiration: number
  ): { title: string; message: string } {
    const expirationDateFormatted = format(
      jobData.expirationDate,
      "dd 'de' MMMM 'de' yyyy",
      { locale: ptBR }
    );

    const contractTypeLabel = this.getContractTypeLabel(jobData.contractType);
    const entityTypeLabel = this.getEntityTypeLabel(jobData.entityType);

    switch (notificationType) {
      case ContractExpirationNotificationType.EXPIRED:
        return {
          title: `${contractTypeLabel} Vencido`,
          message: `O ${contractTypeLabel.toLowerCase()} do ${entityTypeLabel.toLowerCase()} venceu em ${expirationDateFormatted}. Ação imediata necessária.`,
        };

      case ContractExpirationNotificationType.EXPIRING_SOON:
        return {
          title: `${contractTypeLabel} Vencendo em Breve`,
          message: `O ${contractTypeLabel.toLowerCase()} do ${entityTypeLabel.toLowerCase()} vencerá em ${daysUntilExpiration} dia(s) (${expirationDateFormatted}). Prepare a renovação.`,
        };

      case ContractExpirationNotificationType.RENEWAL_REMINDER:
        return {
          title: `Lembrete de Renovação - ${contractTypeLabel}`,
          message: `O ${contractTypeLabel.toLowerCase()} do ${entityTypeLabel.toLowerCase()} vencerá em ${daysUntilExpiration} dias (${expirationDateFormatted}). Considere iniciar o processo de renovação.`,
        };

      default:
        return {
          title: `Notificação de Contrato`,
          message: `O ${contractTypeLabel.toLowerCase()} do ${entityTypeLabel.toLowerCase()} requer atenção. Data de vencimento: ${expirationDateFormatted}.`,
        };
    }
  }

  /**
   * Maps internal notification type to database enum
   */
  private mapNotificationType(
    notificationType?: ContractExpirationNotificationType
  ): $Enums.NotificationType {
    switch (notificationType) {
      case ContractExpirationNotificationType.EXPIRED:
        return 'CONTRACT_EXPIRED';
      case ContractExpirationNotificationType.EXPIRING_SOON:
        return 'CONTRACT_EXPIRING';
      case ContractExpirationNotificationType.RENEWAL_REMINDER:
        return 'CONTRACT_RENEWAL';
      default:
        return 'CONTRACT_GENERAL';
    }
  }

  /**
   * Determines notification priority based on urgency
   */
  private determinePriority(daysUntilExpiration: number): string {
    if (daysUntilExpiration <= 0) return 'URGENT';
    if (daysUntilExpiration <= 7) return 'HIGH';
    if (daysUntilExpiration <= 15) return 'NORMAL';
    return 'LOW';
  }

  /**
   * Calculates when the notification should expire
   */
  private calculateExpirationDate(daysUntilExpiration: number): Date {
    const now = new Date();
    // Notification expires 30 days after the contract expiration
    const expirationDays = Math.max(30, daysUntilExpiration + 30);
    return new Date(now.getTime() + expirationDays * 24 * 60 * 60 * 1000);
  }

  /**
   * Gets user ID from entity information
   * This is a placeholder - implement based on your business logic
   */
  private async getUserIdFromEntity(
    jobData: NotificationProcessorJobData
  ): Promise<string> {
    // This would typically involve querying the backoffice database
    // to find the responsible user for this entity
    // For now, return a default user ID
    return 'system-user';
  }

  /**
   * Gets human-readable contract type label
   */
  private getContractTypeLabel(contractType: string): string {
    const labels: Record<string, string> = {
      SERVICE: 'Contrato de Serviço',
      SUPPLY: 'Contrato de Fornecimento',
      MAINTENANCE: 'Contrato de Manutenção',
      RENTAL: 'Contrato de Locação',
      PARTNERSHIP: 'Contrato de Parceria',
    };
    return labels[contractType] || 'Contrato';
  }

  /**
   * Gets human-readable entity type label
   */
  private getEntityTypeLabel(entityType: string): string {
    const labels: Record<string, string> = {
      SUPPLIER: 'Fornecedor',
      CUSTOMER: 'Cliente',
      PARTNER: 'Parceiro',
      EMPLOYEE: 'Funcionário',
    };
    return labels[entityType] || 'Entidade';
  }
}
