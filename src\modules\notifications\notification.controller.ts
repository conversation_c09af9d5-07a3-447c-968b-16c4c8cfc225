import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { notificationClient } from '../../shared/database/connection';
import { getSSEService } from './sse.service';
import logger from '../../shared/logger/logger';
import { z } from 'zod';

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

const GetNotificationsSchema = z.object({
  userId: z.string(),
  page: z.string().optional().default('1').transform(Number),
  limit: z.string().optional().default('20').transform(Number),
  status: z.enum(['PENDING', 'SENT', 'DELIVERED', 'READ', 'FAILED']).optional(),
  type: z.string().optional(),
  unreadOnly: z
    .string()
    .optional()
    .transform((val) => val === 'true'),
});

const MarkAsReadSchema = z.object({
  notificationIds: z.array(z.string()).min(1),
});

const UpdateNotificationSchema = z.object({
  status: z.enum(['PENDING', 'SENT', 'DELIVERED', 'READ', 'FAILED']).optional(),
  readAt: z
    .string()
    .optional()
    .transform((val) => (val ? new Date(val) : undefined)),
});

const UpdatePreferencesSchema = z.object({
  notificationType: z.string(),
  channel: z.enum([
    'EMAIL',
    'SMS',
    'PUSH',
    'IN_APP',
    'WEBHOOK',
    'SLACK',
    'DISCORD',
    'WHATSAPP',
  ]),
  enabled: z.boolean(),
  emailAddress: z.string().email().optional(),
  phoneNumber: z.string().optional(),
  deviceToken: z.string().optional(),
  webhookUrl: z.string().url().optional(),
  slackChannel: z.string().optional(),
  discordChannel: z.string().optional(),
  WhatsAppNumber: z.string().optional(),
  quietHoursStart: z.string().optional(),
  quietHoursEnd: z.string().optional(),
  timezone: z.string().optional(),
  maxFrequency: z.number().optional(),
  batchNotifications: z.boolean().optional(),
});

// ============================================================================
// NOTIFICATION CONTROLLER
// ============================================================================

export class NotificationController {
  private sseService = getSSEService();

  /**
   * Registers all notification routes
   */
  async registerRoutes(fastify: FastifyInstance): Promise<void> {
    // SSE endpoint for real-time notifications
    fastify.get('/notifications/stream', this.handleSSEConnection.bind(this));

    // Get notifications for a user
    fastify.get('/notifications', this.getNotifications.bind(this));

    // Get notification by ID
    fastify.get('/notifications/:id', this.getNotificationById.bind(this));

    // Mark notifications as read
    fastify.patch(
      '/notifications/mark-read',
      this.markNotificationsAsRead.bind(this)
    );

    // Update notification
    fastify.patch('/notifications/:id', this.updateNotification.bind(this));

    // Delete notification
    fastify.delete('/notifications/:id', this.deleteNotification.bind(this));

    // Get notification statistics
    fastify.get('/notifications/stats', this.getNotificationStats.bind(this));

    // Get unread count
    fastify.get('/notifications/unread-count', this.getUnreadCount.bind(this));

    // User notification preferences
    fastify.get(
      '/notifications/preferences',
      this.getUserPreferences.bind(this)
    );
    fastify.put(
      '/notifications/preferences',
      this.updateUserPreferences.bind(this)
    );

    logger.info('Notification routes registered');
  }

  /**
   * Handles SSE connection for real-time notifications
   */
  async handleSSEConnection(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    await this.sseService.handleConnection(request, reply);
  }

  /**
   * Gets notifications for a user with pagination and filtering
   */
  async getNotifications(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    try {
      const query = GetNotificationsSchema.parse(request.query);
      const { userId, page, limit, status, type, unreadOnly } = query;

      const skip = (page - 1) * limit;

      const whereClause: any = { userId };

      if (status) {
        whereClause.status = status;
      }

      if (type) {
        whereClause.type = type;
      }

      if (unreadOnly) {
        whereClause.readAt = null;
      }

      const [notifications, total] = await Promise.all([
        notificationClient.notification.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
          include: {
            deliveries: {
              select: {
                id: true,
                channel: true,
                status: true,
                sentAt: true,
                deliveredAt: true,
                failedAt: true,
                errorMessage: true,
              },
            },
          },
        }),
        notificationClient.notification.count({ where: whereClause }),
      ]);

      reply.send({
        notifications,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: skip + limit < total,
          hasPrev: page > 1,
        },
      });
    } catch (error) {
      logger.error('Error getting notifications', {
        error,
        query: request.query,
      });
      reply.code(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Gets a specific notification by ID
   */
  async getNotificationById(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    try {
      const { id } = request.params as { id: string };
      const { userId } = request.query as { userId: string };

      if (!userId) {
        reply.code(400).send({ error: 'userId is required' });
        return;
      }

      const notification = await notificationClient.notification.findFirst({
        where: { id, userId },
        include: {
          deliveries: true,
        },
      });

      if (!notification) {
        reply.code(404).send({ error: 'Notification not found' });
        return;
      }

      reply.send({ notification });
    } catch (error) {
      logger.error('Error getting notification by ID', {
        error,
        params: request.params,
      });
      reply.code(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Marks multiple notifications as read
   */
  async markNotificationsAsRead(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    try {
      const { notificationIds } = MarkAsReadSchema.parse(request.body);
      const { userId } = request.query as { userId: string };

      if (!userId) {
        reply.code(400).send({ error: 'userId is required' });
        return;
      }

      const updatedNotifications =
        await notificationClient.notification.updateMany({
          where: {
            id: { in: notificationIds },
            userId,
            readAt: null, // Only update unread notifications
          },
          data: {
            status: 'READ',
            readAt: new Date(),
          },
        });

      logger.info('Notifications marked as read', {
        userId,
        notificationIds,
        updatedCount: updatedNotifications.count,
      });

      reply.send({
        success: true,
        updatedCount: updatedNotifications.count,
      });
    } catch (error) {
      logger.error('Error marking notifications as read', {
        error,
        body: request.body,
      });
      reply.code(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Updates a specific notification
   */
  async updateNotification(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    try {
      const { id } = request.params as { id: string };
      const updateData = UpdateNotificationSchema.parse(request.body);
      const { userId } = request.query as { userId: string };

      if (!userId) {
        reply.code(400).send({ error: 'userId is required' });
        return;
      }

      const notification = await notificationClient.notification.updateMany({
        where: { id, userId },
        data: updateData,
      });

      if (notification.count === 0) {
        reply.code(404).send({ error: 'Notification not found' });
        return;
      }

      reply.send({ success: true });
    } catch (error) {
      logger.error('Error updating notification', {
        error,
        params: request.params,
      });
      reply.code(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Deletes a notification
   */
  async deleteNotification(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    try {
      const { id } = request.params as { id: string };
      const { userId } = request.query as { userId: string };

      if (!userId) {
        reply.code(400).send({ error: 'userId is required' });
        return;
      }

      const notification = await notificationClient.notification.deleteMany({
        where: { id, userId },
      });

      if (notification.count === 0) {
        reply.code(404).send({ error: 'Notification not found' });
        return;
      }

      reply.send({ success: true });
    } catch (error) {
      logger.error('Error deleting notification', {
        error,
        params: request.params,
      });
      reply.code(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Gets notification statistics for a user
   */
  async getNotificationStats(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    try {
      const { userId } = request.query as { userId: string };

      if (!userId) {
        reply.code(400).send({ error: 'userId is required' });
        return;
      }

      const [total, unread, byStatus, byType] = await Promise.all([
        // Total notifications
        notificationClient.notification.count({ where: { userId } }),

        // Unread notifications
        notificationClient.notification.count({
          where: { userId, readAt: null },
        }),

        // By status
        notificationClient.notification.groupBy({
          by: ['status'],
          where: { userId },
          _count: { status: true },
        }),

        // By type
        notificationClient.notification.groupBy({
          by: ['type'],
          where: { userId },
          _count: { type: true },
        }),
      ]);

      const statusStats = byStatus.reduce((acc, item) => {
        acc[item.status] = item._count.status;
        return acc;
      }, {} as Record<string, number>);

      const typeStats = byType.reduce((acc, item) => {
        acc[item.type] = item._count.type;
        return acc;
      }, {} as Record<string, number>);

      reply.send({
        total,
        unread,
        read: total - unread,
        byStatus: statusStats,
        byType: typeStats,
      });
    } catch (error) {
      logger.error('Error getting notification stats', {
        error,
        query: request.query,
      });
      reply.code(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Gets unread notification count for a user
   */
  async getUnreadCount(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    try {
      const { userId } = request.query as { userId: string };

      if (!userId) {
        reply.code(400).send({ error: 'userId is required' });
        return;
      }

      const unreadCount = await notificationClient.notification.count({
        where: { userId, readAt: null },
      });

      reply.send({ unreadCount });
    } catch (error) {
      logger.error('Error getting unread count', {
        error,
        query: request.query,
      });
      reply.code(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Gets user notification preferences
   */
  async getUserPreferences(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    try {
      const { userId } = request.query as { userId: string };

      if (!userId) {
        reply.code(400).send({ error: 'userId is required' });
        return;
      }

      const preferences =
        await notificationClient.userNotificationConfig.findMany({
          where: { userId },
          orderBy: [{ notificationType: 'asc' }, { channel: 'asc' }],
        });

      reply.send({ preferences });
    } catch (error) {
      logger.error('Error getting user preferences', {
        error,
        query: request.query,
      });
      reply.code(500).send({ error: 'Internal server error' });
    }
  }

  /**
   * Updates user notification preferences
   */
  async updateUserPreferences(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    try {
      const preferenceData = UpdatePreferencesSchema.parse(request.body);
      const { userId } = request.query as { userId: string };

      if (!userId) {
        reply.code(400).send({ error: 'userId is required' });
        return;
      }

      const { notificationType, channel, ...updateData } = preferenceData;

      const preference = await notificationClient.userNotificationConfig.upsert(
        {
          where: {
            userId_notificationType_channel: {
              userId,
              notificationType,
              channel,
            },
          },
          update: updateData,
          create: {
            userId,
            notificationType,
            channel,
            ...updateData,
          },
        }
      );

      logger.info('User notification preference updated', {
        userId,
        notificationType,
        channel,
        enabled: preferenceData.enabled,
      });

      reply.send({ preference });
    } catch (error) {
      logger.error('Error updating user preferences', {
        error,
        body: request.body,
      });
      reply.code(500).send({ error: 'Internal server error' });
    }
  }
}
